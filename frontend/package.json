{"name": "task-management-frontend", "version": "0.1.0", "description": "Vue.js frontend for task management system", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"vue": "^3.5.17", "vue-router": "^4.4.5", "pinia": "^2.2.6", "axios": "^1.7.9", "@headlessui/vue": "^1.7.22", "@heroicons/vue": "^2.1.1", "date-fns": "^3.6.0", "chart.js": "^4.4.1", "vue-chartjs": "^5.3.1", "vue-draggable-plus": "^0.5.3", "vue-toastification": "^2.0.0-rc.5", "@vueuse/core": "^11.1.0", "lodash-es": "^4.17.21", "uuid": "^10.0.0"}, "devDependencies": {"@vitejs/plugin-vue": "^5.1.4", "vite": "^5.4.10", "vitest": "^2.1.8", "@vitest/ui": "^2.1.8", "@vue/test-utils": "^2.4.6", "jsdom": "^25.0.1", "eslint": "^9.15.0", "eslint-plugin-vue": "^9.30.0", "prettier": "^3.3.3", "tailwindcss": "^3.4.14", "autoprefixer": "^10.4.20", "postcss": "^8.4.49", "@tailwindcss/forms": "^0.5.9", "@tailwindcss/typography": "^0.5.15"}}