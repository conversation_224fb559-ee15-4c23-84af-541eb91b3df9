/**
 * Authentication Service for Vue.js Frontend
 * Handles JWT authentication with automatic token refresh
 */

import axios from 'axios';

class AuthService {
  constructor() {
    this.baseURL = import.meta.env.VITE_API_URL || 'http://localhost:3000/api';
    this.accessToken = localStorage.getItem('accessToken');
    this.refreshToken = localStorage.getItem('refreshToken');
    this.user = JSON.parse(localStorage.getItem('user') || 'null');
    
    // Setup axios interceptors
    this.setupInterceptors();
  }

  setupInterceptors() {
    // Request interceptor to add auth header
    axios.interceptors.request.use(
      (config) => {
        if (this.accessToken && config.url?.startsWith(this.baseURL)) {
          config.headers.Authorization = `Bearer ${this.accessToken}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor to handle token refresh
    axios.interceptors.response.use(
      (response) => response,
      async (error) => {
        const originalRequest = error.config;

        if (
          error.response?.status === 401 &&
          error.response?.data?.code === 'TOKEN_EXPIRED' &&
          !originalRequest._retry
        ) {
          originalRequest._retry = true;

          try {
            await this.refreshAccessToken();
            originalRequest.headers.Authorization = `Bearer ${this.accessToken}`;
            return axios(originalRequest);
          } catch (refreshError) {
            this.logout();
            window.location.href = '/login';
            return Promise.reject(refreshError);
          }
        }

        return Promise.reject(error);
      }
    );
  }

  /**
   * Login user with email and password
   */
  async login(email, password) {
    try {
      const response = await axios.post(`${this.baseURL}/auth/login`, {
        email,
        password
      });

      if (response.data.success) {
        const { user, tokens } = response.data.data;
        this.setAuthData(user, tokens.accessToken, tokens.refreshToken);
        return user;
      } else {
        throw new Error(response.data.message || 'Login failed');
      }
    } catch (error) {
      console.error('Login error:', error);
      // Handle specific error codes from backend
      if (error.response?.data?.code === 'INVALID_CREDENTIALS') {
        throw new Error('Invalid email or password');
      } else if (error.response?.data?.code === 'VALIDATION_ERROR') {
        throw new Error(error.response.data.details?.join(', ') || 'Validation failed');
      }
      throw new Error(error.response?.data?.error || error.response?.data?.message || 'Login failed');
    }
  }

  /**
   * Register new user
   */
  async register(userData) {
    try {
      const response = await axios.post(`${this.baseURL}/auth/register`, userData);
      
      if (response.data.success) {
        const { user, tokens } = response.data.data;
        this.setAuthData(user, tokens.accessToken, tokens.refreshToken);
        return user;
      }
    } catch (error) {
      const errorMessage = error.response?.data?.error || 'Registration failed';
      const details = error.response?.data?.details;
      
      if (details && Array.isArray(details)) {
        throw new Error(details.join(', '));
      }
      
      throw new Error(errorMessage);
    }
  }

  /**
   * Refresh access token
   */
  async refreshAccessToken() {
    if (!this.refreshToken) {
      throw new Error('No refresh token available');
    }

    try {
      const response = await axios.post(`${this.baseURL}/auth/refresh`, {
        refreshToken: this.refreshToken
      });

      if (response.data.success) {
        this.accessToken = response.data.data.accessToken;
        localStorage.setItem('accessToken', this.accessToken);
        return this.accessToken;
      }
    } catch (error) {
      this.clearAuthData();
      throw new Error('Session expired. Please login again.');
    }
  }

  /**
   * Logout user
   */
  async logout() {
    try {
      if (this.accessToken) {
        await axios.post(`${this.baseURL}/auth/logout`);
      }
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      this.clearAuthData();
    }
  }

  /**
   * Get current user info
   */
  async getCurrentUser() {
    try {
      const response = await axios.get(`${this.baseURL}/auth/me`);
      
      if (response.data.success) {
        this.user = response.data.data.user;
        localStorage.setItem('user', JSON.stringify(this.user));
        return this.user;
      }
    } catch (error) {
      throw new Error('Failed to get user info');
    }
  }

  /**
   * Verify if token is valid
   */
  async verifyToken() {
    if (!this.accessToken) return false;

    try {
      const response = await axios.post(`${this.baseURL}/auth/verify`, {
        token: this.accessToken
      });
      
      return response.data.success;
    } catch (error) {
      return false;
    }
  }

  /**
   * Check if user has required role
   */
  hasRole(requiredRole) {
    if (!this.user) return false;

    // Check both role and roleName properties for compatibility
    const userRole = (this.user.role || this.user.roleName || '').toLowerCase();
    const required = requiredRole.toLowerCase();

    // Admin has access to everything
    if (userRole === 'admin') return true;

    // Manager has access to manager and user routes
    if (userRole === 'manager' && (required === 'manager' || required === 'user')) {
      return true;
    }

    // Exact role match
    return userRole === required;
  }

  /**
   * Check if user is admin
   */
  isAdmin() {
    return this.hasRole('admin');
  }

  /**
   * Check if user is manager or admin
   */
  isManagerOrAdmin() {
    return this.hasRole('admin') || this.hasRole('manager');
  }

  /**
   * Get current access token
   */
  getToken() {
    return this.accessToken;
  }

  /**
   * Set authentication data
   */
  setAuthData(user, accessToken, refreshToken) {
    this.user = user;
    this.accessToken = accessToken;
    this.refreshToken = refreshToken;
    
    localStorage.setItem('user', JSON.stringify(user));
    localStorage.setItem('accessToken', accessToken);
    localStorage.setItem('refreshToken', refreshToken);
  }

  /**
   * Clear authentication data
   */
  clearAuthData() {
    this.user = null;
    this.accessToken = null;
    this.refreshToken = null;
    
    localStorage.removeItem('user');
    localStorage.removeItem('accessToken');
    localStorage.removeItem('refreshToken');
  }

  /**
   * Check if user is authenticated
   */
  isAuthenticated() {
    return !!this.accessToken && !!this.user;
  }

  /**
   * Get user info
   */
  getUser() {
    return this.user;
  }

  /**
   * Get access token
   */
  getAccessToken() {
    return this.accessToken;
  }

  /**
   * Initialize auth service - load tokens from localStorage
   */
  initialize() {
    try {
      const user = localStorage.getItem('user');
      const accessToken = localStorage.getItem('accessToken');
      const refreshToken = localStorage.getItem('refreshToken');

      if (user && accessToken && refreshToken) {
        this.user = JSON.parse(user);
        this.accessToken = accessToken;
        this.refreshToken = refreshToken;
      }
    } catch (error) {
      console.error('Error initializing auth service:', error);
      this.clearAuthData();
    }
  }
}

// Create singleton instance
const authService = new AuthService();

// Initialize immediately
authService.initialize();

export default authService;
