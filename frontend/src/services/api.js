/**
 * API Service
 * Centralized API communication service with request/response interceptors
 */

import axios from 'axios'
import { useAuthStore } from '@/stores/auth'
import { useToast } from 'vue-toastification'
import {
  transformUserFromBackend,
  transformUserToBackend,
  transformTaskFromBackend,
  transformTaskToBackend,
  transformErrorMessage
} from '@/utils/dataTransforms'

// Create axios instance
const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    try {
      const authStore = useAuthStore()
      const token = authStore.token

      if (token) {
        config.headers.Authorization = `Bearer ${token}`
      }
    } catch (error) {
      // Handle case where auth store is not available
      console.warn('Auth store not available:', error)
    }

    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor for error handling with token refresh
api.interceptors.response.use(
  (response) => {
    return response
  },
  async (error) => {
    const originalRequest = error.config

    // Handle network errors
    if (!error.response) {
      error.code = 'NETWORK_ERROR'
      return Promise.reject(error)
    }

    const { status, data } = error.response

    // Handle authentication errors with token refresh
    if (status === 401 && !originalRequest._retry) {
      originalRequest._retry = true

      // Check if it's a token expiration error
      if (data?.code === 'TOKEN_EXPIRED') {
        try {
          // Import auth service dynamically to avoid circular dependency
          const authService = (await import('@/services/auth')).default
          await authService.refreshAccessToken()

          // Retry the original request with new token
          const token = authService.getToken()
          if (token) {
            originalRequest.headers.Authorization = `Bearer ${token}`
            return api(originalRequest)
          }
        } catch (refreshError) {
          // Refresh failed, let the error bubble up
          console.error('Token refresh failed:', refreshError)
        }
      }
    }

    // Add error classification for better handling
    switch (status) {
      case 400:
        if (data?.code === 'VALIDATION_ERROR') {
          error.type = 'VALIDATION_ERROR'
        }
        break
      case 401:
        error.type = 'AUTH_ERROR'
        break
      case 403:
        error.type = 'PERMISSION_ERROR'
        break
      case 404:
        error.type = 'NOT_FOUND_ERROR'
        break
      case 429:
        error.type = 'RATE_LIMIT_ERROR'
        break
      case 500:
      case 502:
      case 503:
      case 504:
        error.type = 'SERVER_ERROR'
        break
      default:
        error.type = 'UNKNOWN_ERROR'
    }

    return Promise.reject(error)
  }
)

// API methods
export const apiService = {
  // Generic methods
  get: (url, config = {}) => api.get(url, config),
  post: (url, data = {}, config = {}) => api.post(url, data, config),
  put: (url, data = {}, config = {}) => api.put(url, data, config),
  patch: (url, data = {}, config = {}) => api.patch(url, data, config),
  delete: (url, config = {}) => api.delete(url, config),
  
  // Authentication
  auth: {
    login: (credentials) => api.post('/auth/login', credentials),
    register: (userData) => api.post('/auth/register', userData),
    logout: () => api.post('/auth/logout'),
    refreshToken: (refreshToken) => api.post('/auth/refresh', { refreshToken }),
    me: () => api.get('/auth/me'),
    verify: (token) => api.post('/auth/verify', { token })
  },
  
  // Users
  users: {
    getAll: (params = {}) => api.get('/users', { params }),
    getById: (id) => api.get(`/users/${id}`),
    create: (userData) => {
      // Map frontend role names to backend roleId
      const roleMap = { admin: 1, user: 2, manager: 3 }
      const backendData = {
        ...userData,
        roleId: roleMap[userData.role] || 2 // default to user
      }
      delete backendData.role
      delete backendData.status // backend doesn't use status in create
      return api.post('/users', backendData)
    },
    update: (id, userData) => {
      // Map frontend role names to backend roleId
      const roleMap = { admin: 1, user: 2, manager: 3 }
      const backendData = {
        ...userData,
        roleId: roleMap[userData.role] || userData.roleId
      }
      delete backendData.role
      delete backendData.status // backend doesn't use status in update
      return api.patch(`/users/${id}`, backendData)
    },
    delete: (id) => api.delete(`/users/${id}`)
  },
  
  // Tasks
  tasks: {
    getAll: (params = {}) => api.get('/tasks', { params }),
    getById: (id) => api.get(`/tasks/${id}`),
    create: (taskData) => {
      // Map frontend data to backend structure
      const backendData = {
        title: taskData.title,
        description: taskData.description || '',
        priority: taskData.priority || 1,
        deadline: taskData.deadline,
        assignedUsers: taskData.assignedUsers || []
      }
      return api.post('/tasks', backendData)
    },
    update: (id, taskData) => {
      // Map frontend status to backend status
      const statusMap = {
        'pending': 'pending',
        'in_progress': 'in_progress',
        'completed': 'completed',
        'cancelled': 'cancelled'
      }
      const backendData = {
        ...taskData,
        status: statusMap[taskData.status] || taskData.status
      }
      return api.patch(`/tasks/${id}`, backendData)
    },
    delete: (id) => api.delete(`/tasks/${id}`),
    updateStatus: (id, status) => {
      const statusMap = {
        'pending': 'pending',
        'in_progress': 'in_progress',
        'completed': 'completed',
        'cancelled': 'cancelled'
      }
      return api.patch(`/tasks/${id}/status`, { status: statusMap[status] || status })
    }
  }
}

export { api, apiService }
export default apiService
