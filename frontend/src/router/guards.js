/**
 * Vue Router Guards for Authentication
 */

import { useAuthStore } from '@/stores/auth.js';

/**
 * Authentication guard - requires user to be logged in
 */
export const requireAuth = async (to, from, next) => {
  const authStore = useAuthStore();

  // If not authenticated, redirect to login
  if (!authStore.isAuthenticated) {
    return next({
      name: 'login',
      query: { redirect: to.fullPath }
    });
  }

  // Verify token is still valid
  const isValid = await authStore.verifyAuth();
  if (!isValid) {
    return next({
      name: 'login',
      query: { redirect: to.fullPath }
    });
  }

  next();
};

/**
 * Guest guard - redirects authenticated users away from auth pages
 */
export const requireGuest = (to, from, next) => {
  const authStore = useAuthStore();

  if (authStore.isAuthenticated) {
    return next({ name: 'dashboard' });
  }

  next();
};

/**
 * Admin guard - requires admin role
 */
export const requireAdmin = async (to, from, next) => {
  const authStore = useAuthStore();

  // First check authentication
  if (!authStore.isAuthenticated) {
    return next({
      name: 'login',
      query: { redirect: to.fullPath }
    });
  }

  // Verify token and check admin role
  const isValid = await authStore.verifyAuth();
  if (!isValid) {
    return next({
      name: 'login',
      query: { redirect: to.fullPath }
    });
  }

  if (!authStore.isAdmin) {
    return next({
      name: 'unauthorized',
      query: { message: 'Admin access required' }
    });
  }

  next();
};

/**
 * Manager or Admin guard - requires manager or admin role
 */
export const requireManagerOrAdmin = async (to, from, next) => {
  const authStore = useAuthStore();

  // First check authentication
  if (!authStore.isAuthenticated) {
    return next({
      name: 'login',
      query: { redirect: to.fullPath }
    });
  }

  // Verify token and check role
  const isValid = await authStore.verifyAuth();
  if (!isValid) {
    return next({
      name: 'login',
      query: { redirect: to.fullPath }
    });
  }

  if (!authStore.isManagerOrAdmin) {
    return next({
      name: 'unauthorized',
      query: { message: 'Manager or Admin access required' }
    });
  }

  next();
};

/**
 * Role-based guard factory
 */
export const requireRole = (requiredRole) => {
  return async (to, from, next) => {
    const authStore = useAuthStore();

    // First check authentication
    if (!authStore.isAuthenticated) {
      return next({
        name: 'login',
        query: { redirect: to.fullPath }
      });
    }

    // Verify token and check role
    const isValid = await authStore.verifyAuth();
    if (!isValid) {
      return next({
        name: 'login',
        query: { redirect: to.fullPath }
      });
    }

    if (!authStore.hasRole(requiredRole)) {
      return next({
        name: 'unauthorized',
        query: { message: `${requiredRole} access required` }
      });
    }

    next();
  };
};
