/**
 * Vue Router Configuration
 * Defines all application routes with authentication guards
 */

import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

// Import views
import LoginView from '@/views/auth/LoginView.vue'
import RegisterView from '@/views/auth/RegisterView.vue'
import DashboardView from '@/views/DashboardView.vue'
import TasksView from '@/views/tasks/TasksView.vue'
import TaskDetailView from '@/views/tasks/TaskDetailView.vue'
import GanttChartView from '@/views/tasks/GanttChartView.vue'
import UsersView from '@/views/users/UsersView.vue'
import UserDetailView from '@/views/users/UserDetailView.vue'
import ProfileView from '@/views/ProfileView.vue'
import NotFoundView from '@/views/NotFoundView.vue'

const routes = [
  // Authentication routes
  {
    path: '/login',
    name: 'login',
    component: LoginView,
    meta: {
      requiresGuest: true,
      title: 'Login'
    }
  },
  {
    path: '/register',
    name: 'register',
    component: RegisterView,
    meta: {
      requiresGuest: true,
      title: 'Register'
    }
  },

  // Protected routes
  {
    path: '/',
    name: 'dashboard',
    component: DashboardView,
    meta: {
      requiresAuth: true,
      title: 'Dashboard'
    }
  },
  {
    path: '/tasks',
    name: 'tasks',
    component: TasksView,
    meta: {
      requiresAuth: true,
      title: 'Tasks'
    }
  },
  {
    path: '/tasks/:id',
    name: 'task-detail',
    component: TaskDetailView,
    meta: {
      requiresAuth: true,
      title: 'Task Details'
    }
  },
  {
    path: '/gantt',
    name: 'gantt-chart',
    component: GanttChartView,
    meta: {
      requiresAuth: true,
      title: 'Gantt Chart'
    }
  },
  {
    path: '/profile',
    name: 'profile',
    component: ProfileView,
    meta: {
      requiresAuth: true,
      title: 'Profile'
    }
  },

  // Admin/Manager only routes
  {
    path: '/users',
    name: 'users',
    component: UsersView,
    meta: {
      requiresAuth: true,
      requiresRole: 'admin',
      title: 'User Management'
    }
  },
  {
    path: '/users/:id',
    name: 'user-detail',
    component: UserDetailView,
    meta: {
      requiresAuth: true,
      requiresRole: 'admin',
      title: 'User Details'
    }
  },

  // Catch all route - must be last
  {
    path: '/:pathMatch(.*)*',
    name: 'not-found',
    component: NotFoundView,
    meta: {
      title: 'Page Not Found'
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// Global navigation guards
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()
  
  // Update document title
  document.title = to.meta.title ? `${to.meta.title} - Task Management` : 'Task Management'
  
  // Check if route requires authentication
  if (to.meta.requiresAuth) {
    if (!authStore.isAuthenticated) {
      // Redirect to login if not authenticated
      next({
        name: 'login',
        query: { redirect: to.fullPath }
      })
      return
    }
    
    // Check role-based access
    if (to.meta.requiresRole) {
      if (!authStore.hasRole(to.meta.requiresRole)) {
        // Redirect to dashboard if insufficient permissions
        next({ name: 'dashboard' })
        return
      }
    }
  }
  
  // Check if route requires guest (not authenticated)
  if (to.meta.requiresGuest && authStore.isAuthenticated) {
    // Redirect to dashboard if already authenticated
    next({ name: 'dashboard' })
    return
  }
  
  next()
})

// Global after guards
router.afterEach((to, from) => {
  // You can add analytics tracking here
  console.log(`Navigated from ${from.name} to ${to.name}`)
})

export default router
