/**
 * Main CSS file for the Task Management Frontend
 * Includes Tailwind CSS base, components, and utilities
 */

@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Custom CSS Variables for Theme Colors */
:root {
  /* Light theme colors */
  --color-primary: #3b82f6;
  --color-primary-dark: #2563eb;
  --color-secondary: #6b7280;
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
  --color-info: #06b6d4;
  
  /* Background colors */
  --color-bg-primary: #ffffff;
  --color-bg-secondary: #f9fafb;
  --color-bg-tertiary: #f3f4f6;
  
  /* Text colors */
  --color-text-primary: #111827;
  --color-text-secondary: #6b7280;
  --color-text-tertiary: #9ca3af;
  
  /* Border colors */
  --color-border-primary: #e5e7eb;
  --color-border-secondary: #d1d5db;
}

/* Dark theme colors */
.dark {
  --color-primary: #3b82f6;
  --color-primary-dark: #2563eb;
  --color-secondary: #9ca3af;
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
  --color-info: #06b6d4;
  
  /* Background colors */
  --color-bg-primary: #111827;
  --color-bg-secondary: #1f2937;
  --color-bg-tertiary: #374151;
  
  /* Text colors */
  --color-text-primary: #f9fafb;
  --color-text-secondary: #d1d5db;
  --color-text-tertiary: #9ca3af;
  
  /* Border colors */
  --color-border-primary: #374151;
  --color-border-secondary: #4b5563;
}

/* Base styles */
@layer base {
  html {
    scroll-behavior: smooth;
  }
  
  body {
    @apply font-sans antialiased;
    background-color: var(--color-bg-primary);
    color: var(--color-text-primary);
    transition: background-color 0.2s ease, color 0.2s ease;
  }
  
  /* Custom scrollbar */
  ::-webkit-scrollbar {
    @apply w-2;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-gray-100 dark:bg-gray-800;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 dark:bg-gray-600 rounded-full;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400 dark:bg-gray-500;
  }
}

/* Component styles */
@layer components {
  /* Button styles */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }
  
  .btn-primary {
    @apply btn bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
  }
  
  .btn-secondary {
    @apply btn bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500;
  }
  
  .btn-success {
    @apply btn bg-green-600 text-white hover:bg-green-700 focus:ring-green-500;
  }
  
  .btn-warning {
    @apply btn bg-yellow-600 text-white hover:bg-yellow-700 focus:ring-yellow-500;
  }
  
  .btn-danger {
    @apply btn bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
  }
  
  .btn-outline {
    @apply btn bg-transparent border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800;
  }
  
  /* Form styles */
  .form-input {
    @apply block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200;
  }
  
  .form-label {
    @apply block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1;
  }
  
  .form-error {
    @apply text-sm text-red-600 dark:text-red-400 mt-1;
  }
  
  /* Card styles */
  .card {
    @apply bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 transition-colors duration-200;
  }
  
  .card-header {
    @apply px-6 py-4 border-b border-gray-200 dark:border-gray-700;
  }
  
  .card-body {
    @apply px-6 py-4;
  }
  
  .card-footer {
    @apply px-6 py-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900 rounded-b-lg;
  }
  
  /* Navigation styles */
  .nav-link {
    @apply flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200;
  }
  
  .nav-link-active {
    @apply nav-link bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300;
  }
  
  .nav-link-inactive {
    @apply nav-link text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100;
  }
  
  /* Modal styles */
  .modal-overlay {
    @apply fixed inset-0 bg-black bg-opacity-50 transition-opacity z-50;
  }
  
  .modal-container {
    @apply fixed inset-0 z-50 overflow-y-auto;
  }
  
  .modal-content {
    @apply relative bg-white dark:bg-gray-800 rounded-lg shadow-xl transform transition-all;
  }
  
  /* Loading spinner */
  .spinner {
    @apply animate-spin rounded-full border-2 border-gray-300 border-t-blue-600;
  }
  
  /* Status badges */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .badge-success {
    @apply badge bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200;
  }
  
  .badge-warning {
    @apply badge bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200;
  }
  
  .badge-error {
    @apply badge bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200;
  }
  
  .badge-info {
    @apply badge bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200;
  }
  
  .badge-secondary {
    @apply badge bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200;
  }
}

/* Utility styles */
@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  
  .transition-theme {
    transition: background-color 0.2s ease, color 0.2s ease, border-color 0.2s ease;
  }
  
  /* Mobile-first responsive utilities */
  .mobile-padding {
    @apply px-4 sm:px-6 lg:px-8;
  }
  
  .mobile-margin {
    @apply mx-4 sm:mx-6 lg:mx-8;
  }
}

/* Animation keyframes */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Animation utilities */
.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}

.animate-slide-in-up {
  animation: slideInUp 0.3s ease-out;
}

.animate-slide-in-down {
  animation: slideInDown 0.3s ease-out;
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-break-before {
    page-break-before: always;
  }
  
  .print-break-after {
    page-break-after: always;
  }
}
