<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <div class="mobile-padding py-8">
      <!-- Header -->
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
          Dashboard
        </h1>
        <p class="mt-2 text-gray-600 dark:text-gray-400">
          Welcome back, {{ authStore.userFullName }}! Here's an overview of your tasks.
        </p>
      </div>

      <!-- Stats Cards -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="card">
          <div class="card-body">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <ClipboardDocumentListIcon class="h-8 w-8 text-blue-600" />
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-500 dark:text-gray-400">
                  Total Tasks
                </p>
                <p class="text-2xl font-bold text-gray-900 dark:text-white">
                  {{ stats.totalTasks }}
                </p>
              </div>
            </div>
          </div>
        </div>

        <div class="card">
          <div class="card-body">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <ClockIcon class="h-8 w-8 text-yellow-600" />
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-500 dark:text-gray-400">
                  In Progress
                </p>
                <p class="text-2xl font-bold text-gray-900 dark:text-white">
                  {{ stats.inProgressTasks }}
                </p>
              </div>
            </div>
          </div>
        </div>

        <div class="card">
          <div class="card-body">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <CheckCircleIcon class="h-8 w-8 text-green-600" />
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-500 dark:text-gray-400">
                  Completed
                </p>
                <p class="text-2xl font-bold text-gray-900 dark:text-white">
                  {{ stats.completedTasks }}
                </p>
              </div>
            </div>
          </div>
        </div>

        <div class="card">
          <div class="card-body">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <ExclamationTriangleIcon class="h-8 w-8 text-red-600" />
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-500 dark:text-gray-400">
                  Overdue
                </p>
                <p class="text-2xl font-bold text-gray-900 dark:text-white">
                  {{ stats.overdueTasks }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Recent Tasks -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- My Recent Tasks -->
        <div class="card">
          <div class="card-header">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">
              Recent Tasks
            </h3>
          </div>
          <div class="card-body">
            <div v-if="loading" class="flex justify-center py-8">
              <div class="spinner h-8 w-8"></div>
            </div>
            <div v-else-if="recentTasks.length === 0" class="text-center py-8">
              <ClipboardDocumentListIcon class="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p class="text-gray-500 dark:text-gray-400">No tasks found</p>
            </div>
            <div v-else class="space-y-4">
              <div
                v-for="task in recentTasks"
                :key="task.id"
                class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
              >
                <div class="flex-1">
                  <h4 class="text-sm font-medium text-gray-900 dark:text-white">
                    {{ task.title }}
                  </h4>
                  <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    {{ formatDate(task.deadline) }}
                  </p>
                </div>
                <div class="flex items-center space-x-2">
                  <span
                    class="badge"
                    :class="getStatusBadgeClass(task.status)"
                  >
                    {{ task.status }}
                  </span>
                  <span
                    class="badge"
                    :class="getPriorityBadgeClass(task.priority)"
                  >
                    {{ task.priority }}
                  </span>
                </div>
              </div>
            </div>
          </div>
          <div class="card-footer">
            <router-link
              to="/tasks"
              class="text-sm text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300"
            >
              View all tasks →
            </router-link>
          </div>
        </div>

        <!-- Quick Actions -->
        <div class="card">
          <div class="card-header">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">
              Quick Actions
            </h3>
          </div>
          <div class="card-body space-y-4">
            <router-link
              to="/tasks"
              class="block p-4 bg-blue-50 dark:bg-blue-900 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-800 transition-colors duration-200"
            >
              <div class="flex items-center">
                <PlusIcon class="h-6 w-6 text-blue-600 dark:text-blue-400" />
                <div class="ml-3">
                  <p class="text-sm font-medium text-blue-900 dark:text-blue-100">
                    Create New Task
                  </p>
                  <p class="text-xs text-blue-700 dark:text-blue-300">
                    Add a new task to your project
                  </p>
                </div>
              </div>
            </router-link>

            <router-link
              to="/gantt"
              class="block p-4 bg-green-50 dark:bg-green-900 rounded-lg hover:bg-green-100 dark:hover:bg-green-800 transition-colors duration-200"
            >
              <div class="flex items-center">
                <ChartBarIcon class="h-6 w-6 text-green-600 dark:text-green-400" />
                <div class="ml-3">
                  <p class="text-sm font-medium text-green-900 dark:text-green-100">
                    View Gantt Chart
                  </p>
                  <p class="text-xs text-green-700 dark:text-green-300">
                    Visualize project timeline
                  </p>
                </div>
              </div>
            </router-link>

            <router-link
              v-if="authStore.isAdmin"
              to="/users"
              class="block p-4 bg-purple-50 dark:bg-purple-900 rounded-lg hover:bg-purple-100 dark:hover:bg-purple-800 transition-colors duration-200"
            >
              <div class="flex items-center">
                <UsersIcon class="h-6 w-6 text-purple-600 dark:text-purple-400" />
                <div class="ml-3">
                  <p class="text-sm font-medium text-purple-900 dark:text-purple-100">
                    Manage Users
                  </p>
                  <p class="text-xs text-purple-700 dark:text-purple-300">
                    Add or edit user accounts
                  </p>
                </div>
              </div>
            </router-link>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { format } from 'date-fns'
import {
  ClipboardDocumentListIcon,
  ClockIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  PlusIcon,
  ChartBarIcon,
  UsersIcon
} from '@heroicons/vue/24/outline'

// Composables
const authStore = useAuthStore()

// Reactive state
const loading = ref(true)
const recentTasks = ref([])
const stats = ref({
  totalTasks: 0,
  inProgressTasks: 0,
  completedTasks: 0,
  overdueTasks: 0
})

// Methods
const formatDate = (date) => {
  if (!date) return 'No deadline'
  return format(new Date(date), 'MMM dd, yyyy')
}

const getStatusBadgeClass = (status) => {
  switch (status?.toLowerCase()) {
    case 'completed':
      return 'badge-success'
    case 'in_progress':
      return 'badge-info'
    case 'cancelled':
      return 'badge-error'
    default:
      return 'badge-secondary'
  }
}

const getPriorityBadgeClass = (priority) => {
  switch (priority?.toLowerCase()) {
    case 'urgent':
      return 'badge-error'
    case 'high':
      return 'badge-warning'
    case 'medium':
      return 'badge-info'
    default:
      return 'badge-secondary'
  }
}

const fetchDashboardData = async () => {
  try {
    loading.value = true
    
    // TODO: Replace with actual API calls
    // Simulated data for now
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    stats.value = {
      totalTasks: 12,
      inProgressTasks: 5,
      completedTasks: 6,
      overdueTasks: 1
    }
    
    recentTasks.value = [
      {
        id: 1,
        title: 'Setup Project Environment',
        status: 'completed',
        priority: 'high',
        deadline: new Date('2024-01-15')
      },
      {
        id: 2,
        title: 'Design Database Schema',
        status: 'in_progress',
        priority: 'high',
        deadline: new Date('2024-01-20')
      },
      {
        id: 3,
        title: 'Implement User Authentication',
        status: 'pending',
        priority: 'medium',
        deadline: new Date('2024-01-25')
      }
    ]
  } catch (error) {
    console.error('Error fetching dashboard data:', error)
  } finally {
    loading.value = false
  }
}

// Lifecycle
onMounted(() => {
  fetchDashboardData()
})
</script>
