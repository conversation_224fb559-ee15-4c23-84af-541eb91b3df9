<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <div class="mobile-padding py-8">
      <!-- Header -->
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
        <div>
          <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
            User Management
          </h1>
          <p class="mt-2 text-gray-600 dark:text-gray-400">
            Manage user accounts and permissions
          </p>
        </div>
        <div class="mt-4 sm:mt-0">
          <BaseButton
            variant="primary"
            :icon-left="PlusIcon"
            @click="openCreateModal"
          >
            Add User
          </BaseButton>
        </div>
      </div>

      <!-- Users List -->
      <BaseCard>
        <template #body>
          <div v-if="loading" class="flex justify-center py-8">
            <LoadingSpinner size="lg" text="Loading users..." />
          </div>
          <div v-else-if="users.length === 0" class="text-center py-8">
            <UsersIcon class="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p class="text-gray-500 dark:text-gray-400">No users found</p>
          </div>
          <div v-else class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead class="bg-gray-50 dark:bg-gray-800">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    User
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Role
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Status
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Created
                  </th>
                  <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                <tr
                  v-for="user in users"
                  :key="user.id"
                  class="hover:bg-gray-50 dark:hover:bg-gray-800"
                >
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <div class="flex-shrink-0 h-10 w-10">
                        <div class="h-10 w-10 bg-blue-600 rounded-full flex items-center justify-center">
                          <span class="text-sm font-medium text-white">
                            {{ getUserInitials(user) }}
                          </span>
                        </div>
                      </div>
                      <div class="ml-4">
                        <div class="text-sm font-medium text-gray-900 dark:text-white">
                          {{ user.firstName }} {{ user.lastName }}
                        </div>
                        <div class="text-sm text-gray-500 dark:text-gray-400">
                          {{ user.email }}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <BaseBadge
                      :variant="getRoleBadgeVariant(user.role)"
                    >
                      {{ user.role }}
                    </BaseBadge>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <BaseBadge
                      :variant="getStatusBadgeVariant(user.status)"
                    >
                      {{ user.status }}
                    </BaseBadge>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    {{ formatDate(user.createdAt) }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div class="flex items-center justify-end space-x-2">
                      <button
                        @click="viewUser(user)"
                        class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                        title="View user"
                      >
                        <EyeIcon class="h-5 w-5" />
                      </button>
                      <button
                        @click="editUser(user)"
                        class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                        title="Edit user"
                      >
                        <PencilIcon class="h-5 w-5" />
                      </button>
                      <button
                        v-if="user.id !== authStore.user?.id"
                        @click="deleteUser(user)"
                        class="p-2 text-gray-400 hover:text-red-600"
                        title="Delete user"
                      >
                        <TrashIcon class="h-5 w-5" />
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </template>
      </BaseCard>
    </div>

    <!-- User Modal -->
    <UserModal
      v-model="showUserModal"
      :user="selectedUser"
      @user-created="handleUserCreated"
      @user-updated="handleUserUpdated"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useToast } from 'vue-toastification'
import { format } from 'date-fns'
import {
  PlusIcon,
  UsersIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon
} from '@heroicons/vue/24/outline'
import { BaseCard, BaseButton, BaseBadge, LoadingSpinner } from '@/components/ui'
import UserModal from '@/components/users/UserModal.vue'
import { apiService } from '@/services/api'

// Composables
const router = useRouter()
const authStore = useAuthStore()
const toast = useToast()

// Reactive state
const loading = ref(true)
const showUserModal = ref(false)
const selectedUser = ref(null)
const users = ref([])

// Methods
const formatDate = (date) => {
  if (!date) return 'Unknown'
  return format(new Date(date), 'MMM dd, yyyy')
}

const getUserInitials = (user) => {
  const firstInitial = user.firstName?.charAt(0)?.toUpperCase() || ''
  const lastInitial = user.lastName?.charAt(0)?.toUpperCase() || ''
  return firstInitial + lastInitial || 'U'
}

const getRoleBadgeVariant = (role) => {
  switch (role?.toLowerCase()) {
    case 'admin':
      return 'danger'
    case 'manager':
      return 'warning'
    default:
      return 'info'
  }
}

const getStatusBadgeVariant = (status) => {
  switch (status?.toLowerCase()) {
    case 'active':
      return 'success'
    case 'suspended':
      return 'warning'
    case 'deleted':
      return 'danger'
    default:
      return 'default'
  }
}

const viewUser = (user) => {
  router.push(`/users/${user.id}`)
}

const openCreateModal = () => {
  selectedUser.value = null
  showUserModal.value = true
}

const editUser = (user) => {
  selectedUser.value = user
  showUserModal.value = true
}

const deleteUser = async (user) => {
  if (confirm(`Are you sure you want to delete ${user.firstName} ${user.lastName}?`)) {
    try {
      await apiService.users.delete(user.id)
      users.value = users.value.filter(u => u.id !== user.id)
      toast.success('User deleted successfully!')
    } catch (error) {
      console.error('Error deleting user:', error)
      toast.error('Failed to delete user')
    }
  }
}

const handleUserCreated = (newUser) => {
  users.value.unshift(newUser)
}

const handleUserUpdated = (updatedUser) => {
  const index = users.value.findIndex(u => u.id === updatedUser.id)
  if (index !== -1) {
    users.value[index] = updatedUser
  }
}

const fetchUsers = async () => {
  try {
    loading.value = true

    // Try to fetch from API, fall back to mock data
    try {
      const response = await apiService.users.getAll()
      users.value = response.data.users || response.data
    } catch (apiError) {
      console.warn('API not available, using mock data:', apiError)

      // Mock data for development
      users.value = [
        {
          id: 1,
          firstName: 'Admin',
          lastName: 'User',
          username: 'admin',
          email: '<EMAIL>',
          role: 'admin',
          status: 'active',
          createdAt: new Date('2024-01-01')
        },
        {
          id: 2,
          firstName: 'Manager',
          lastName: 'User',
          username: 'manager',
          email: '<EMAIL>',
          role: 'manager',
          status: 'active',
          createdAt: new Date('2024-01-02')
        },
        {
          id: 3,
          firstName: 'Regular',
          lastName: 'User',
          username: 'user',
          email: '<EMAIL>',
          role: 'user',
          status: 'active',
          createdAt: new Date('2024-01-03')
        }
      ]
    }
  } catch (error) {
    console.error('Error fetching users:', error)
    toast.error('Failed to load users')
  } finally {
    loading.value = false
  }
}

// Lifecycle
onMounted(() => {
  fetchUsers()
})
</script>
