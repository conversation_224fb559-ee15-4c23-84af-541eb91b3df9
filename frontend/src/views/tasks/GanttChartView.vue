<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <div class="mobile-padding py-8">
      <!-- Header -->
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
          Gantt Chart
        </h1>
        <p class="mt-2 text-gray-600 dark:text-gray-400">
          Visualize project timeline and task dependencies
        </p>
      </div>

      <!-- Gantt Chart Container -->
      <div class="card">
        <div class="card-body">
          <div class="text-center py-16">
            <ChartBarIcon class="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
              Gantt Chart Coming Soon
            </h3>
            <p class="text-gray-600 dark:text-gray-400 mb-6">
              Interactive Gantt chart with drag-and-drop functionality will be implemented here.
            </p>
            <div class="space-y-2 text-sm text-gray-500 dark:text-gray-400">
              <p>• Drag-and-drop task scheduling</p>
              <p>• Task dependencies visualization</p>
              <p>• Timeline management</p>
              <p>• Progress tracking</p>
              <p>• Responsive design for mobile and desktop</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ChartBarIcon } from '@heroicons/vue/24/outline'

// TODO: Implement Gantt chart functionality
// This will include:
// - Interactive timeline
// - Drag and drop task scheduling
// - Task dependencies
// - Progress visualization
// - Responsive design
</script>
