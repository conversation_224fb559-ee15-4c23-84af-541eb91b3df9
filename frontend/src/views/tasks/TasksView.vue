<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <div class="mobile-padding py-8">
      <!-- Header -->
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
        <div>
          <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
            Tasks
          </h1>
          <p class="mt-2 text-gray-600 dark:text-gray-400">
            Manage and track your tasks
          </p>
        </div>
        <div class="mt-4 sm:mt-0">
          <button
            v-if="authStore.isManagerOrAdmin"
            @click="showCreateModal = true"
            class="btn-primary"
          >
            <PlusIcon class="h-5 w-5 mr-2" />
            Create Task
          </button>
        </div>
      </div>

      <!-- Filters -->
      <div class="card mb-6">
        <div class="card-body">
          <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label class="form-label">Status</label>
              <select v-model="filters.status" class="form-input">
                <option value="">All Status</option>
                <option value="pending">Pending</option>
                <option value="in_progress">In Progress</option>
                <option value="completed">Completed</option>
                <option value="cancelled">Cancelled</option>
              </select>
            </div>
            <div>
              <label class="form-label">Priority</label>
              <select v-model="filters.priority" class="form-input">
                <option value="">All Priorities</option>
                <option value="low">Low</option>
                <option value="medium">Medium</option>
                <option value="high">High</option>
                <option value="urgent">Urgent</option>
              </select>
            </div>
            <div>
              <label class="form-label">Assigned To</label>
              <select v-model="filters.assignedTo" class="form-input">
                <option value="">All Users</option>
                <option value="me">My Tasks</option>
                <!-- TODO: Add other users -->
              </select>
            </div>
            <div class="flex items-end">
              <button
                @click="resetFilters"
                class="btn-outline w-full"
              >
                Reset Filters
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Tasks List -->
      <div class="card">
        <div class="card-body">
          <div v-if="loading" class="flex justify-center py-8">
            <div class="spinner h-8 w-8"></div>
          </div>
          <div v-else-if="filteredTasks.length === 0" class="text-center py-8">
            <ClipboardDocumentListIcon class="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p class="text-gray-500 dark:text-gray-400">No tasks found</p>
          </div>
          <div v-else class="space-y-4">
            <div
              v-for="task in filteredTasks"
              :key="task.id"
              class="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow duration-200"
            >
              <div class="flex items-start justify-between">
                <div class="flex-1">
                  <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                    {{ task.title }}
                  </h3>
                  <p class="text-gray-600 dark:text-gray-400 mt-1">
                    {{ task.description }}
                  </p>
                  <div class="flex items-center space-x-4 mt-3">
                    <span
                      class="badge"
                      :class="getStatusBadgeClass(task.status)"
                    >
                      {{ task.status }}
                    </span>
                    <span
                      class="badge"
                      :class="getPriorityBadgeClass(task.priority)"
                    >
                      {{ task.priority }}
                    </span>
                    <span class="text-sm text-gray-500 dark:text-gray-400">
                      Due: {{ formatDate(task.deadline) }}
                    </span>
                  </div>
                </div>
                <div class="flex items-center space-x-2 ml-4">
                  <button
                    @click="viewTask(task)"
                    class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                    title="View task"
                  >
                    <EyeIcon class="h-5 w-5" />
                  </button>
                  <button
                    v-if="canEditTask(task)"
                    @click="editTask(task)"
                    class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                    title="Edit task"
                  >
                    <PencilIcon class="h-5 w-5" />
                  </button>
                  <button
                    v-if="canDeleteTask(task)"
                    @click="deleteTask(task)"
                    class="p-2 text-gray-400 hover:text-red-600"
                    title="Delete task"
                  >
                    <TrashIcon class="h-5 w-5" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Create Task Modal -->
    <!-- TODO: Implement create task modal -->
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { format } from 'date-fns'
import {
  PlusIcon,
  ClipboardDocumentListIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon
} from '@heroicons/vue/24/outline'

// Composables
const router = useRouter()
const authStore = useAuthStore()

// Reactive state
const loading = ref(true)
const showCreateModal = ref(false)
const tasks = ref([])

const filters = reactive({
  status: '',
  priority: '',
  assignedTo: ''
})

// Computed properties
const filteredTasks = computed(() => {
  let filtered = tasks.value

  if (filters.status) {
    filtered = filtered.filter(task => task.status === filters.status)
  }

  if (filters.priority) {
    filtered = filtered.filter(task => task.priority === filters.priority)
  }

  if (filters.assignedTo === 'me') {
    filtered = filtered.filter(task => task.assignedTo === authStore.user?.id)
  }

  return filtered
})

// Methods
const formatDate = (date) => {
  if (!date) return 'No deadline'
  return format(new Date(date), 'MMM dd, yyyy')
}

const getStatusBadgeClass = (status) => {
  switch (status?.toLowerCase()) {
    case 'completed':
      return 'badge-success'
    case 'in_progress':
      return 'badge-info'
    case 'cancelled':
      return 'badge-error'
    default:
      return 'badge-secondary'
  }
}

const getPriorityBadgeClass = (priority) => {
  switch (priority?.toLowerCase()) {
    case 'urgent':
      return 'badge-error'
    case 'high':
      return 'badge-warning'
    case 'medium':
      return 'badge-info'
    default:
      return 'badge-secondary'
  }
}

const canEditTask = (task) => {
  return authStore.isManagerOrAdmin || task.assignedTo === authStore.user?.id
}

const canDeleteTask = (task) => {
  return authStore.isManagerOrAdmin
}

const resetFilters = () => {
  filters.status = ''
  filters.priority = ''
  filters.assignedTo = ''
}

const viewTask = (task) => {
  router.push(`/tasks/${task.id}`)
}

const editTask = (task) => {
  // TODO: Implement edit task functionality
  console.log('Edit task:', task)
}

const deleteTask = (task) => {
  // TODO: Implement delete task functionality
  console.log('Delete task:', task)
}

const fetchTasks = async () => {
  try {
    loading.value = true
    
    // TODO: Replace with actual API call
    // Simulated data for now
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    tasks.value = [
      {
        id: 1,
        title: 'Setup Project Environment',
        description: 'Initialize the development environment and configure all necessary tools',
        status: 'completed',
        priority: 'high',
        deadline: new Date('2024-01-15'),
        assignedTo: authStore.user?.id,
        createdBy: 1
      },
      {
        id: 2,
        title: 'Design Database Schema',
        description: 'Create comprehensive database schema for the task management system',
        status: 'in_progress',
        priority: 'high',
        deadline: new Date('2024-01-20'),
        assignedTo: authStore.user?.id,
        createdBy: 1
      },
      {
        id: 3,
        title: 'Implement User Authentication',
        description: 'Build secure user authentication system with JWT tokens',
        status: 'pending',
        priority: 'medium',
        deadline: new Date('2024-01-25'),
        assignedTo: 2,
        createdBy: 1
      }
    ]
  } catch (error) {
    console.error('Error fetching tasks:', error)
  } finally {
    loading.value = false
  }
}

// Lifecycle
onMounted(() => {
  fetchTasks()
})
</script>
