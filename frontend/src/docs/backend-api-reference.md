# Backend API Reference

## Overview
This document provides a comprehensive reference for the Express.js backend API endpoints, authentication patterns, and data structures for the task management system.

## Base Configuration
- **Base URL**: `http://localhost:3000/api`
- **Authentication**: JWT Bearer tokens
- **Content-Type**: `application/json`
- **Rate Limiting**: 100 requests per 15 minutes per IP

## Authentication Endpoints

### POST /api/auth/login
**Description**: Authenticate user and return JWT tokens

**Request Body**:
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Success Response (200)**:
```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    "user": {
      "id": 1,
      "username": "johndoe",
      "email": "<EMAIL>",
      "firstName": "John",
      "middleName": "",
      "lastName": "Doe",
      "role": "user",
      "roleId": 2,
      "roleName": "user",
      "status": "active",
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    },
    "tokens": {
      "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "expiresIn": "15m"
    }
  }
}
```

**Error Responses**:
- `401 INVALID_CREDENTIALS`: Invalid email or password
- `400 VALIDATION_ERROR`: Request validation failed

### POST /api/auth/register
**Description**: Create new user account

**Request Body**:
```json
{
  "username": "johndoe",
  "email": "<EMAIL>",
  "password": "password123",
  "firstName": "John",
  "middleName": "",
  "lastName": "Doe",
  "roleId": 2
}
```

**Success Response (201)**: Same as login response

**Error Responses**:
- `409 USER_EXISTS`: User with email/username already exists
- `400 VALIDATION_ERROR`: Request validation failed

### POST /api/auth/refresh
**Description**: Refresh access token using refresh token

**Request Body**:
```json
{
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**Success Response (200)**:
```json
{
  "success": true,
  "message": "Token refreshed successfully",
  "data": {
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expiresIn": "15m"
  }
}
```

**Error Responses**:
- `401 REFRESH_TOKEN_EXPIRED`: Refresh token expired
- `401 INVALID_REFRESH_TOKEN`: Invalid refresh token

### POST /api/auth/logout
**Description**: Invalidate current session and blacklist token

**Headers**: `Authorization: Bearer <access_token>`

**Success Response (200)**:
```json
{
  "success": true,
  "message": "Logout successful"
}
```

### GET /api/auth/me
**Description**: Get current authenticated user information

**Headers**: `Authorization: Bearer <access_token>`

**Success Response (200)**:
```json
{
  "success": true,
  "data": {
    "user": {
      "id": 1,
      "username": "johndoe",
      "email": "<EMAIL>",
      "firstName": "John",
      "middleName": "",
      "lastName": "Doe",
      "role": "user",
      "roleId": 2,
      "roleName": "user",
      "status": "active",
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    }
  }
}
```

### POST /api/auth/verify
**Description**: Verify token validity

**Request Body**:
```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**Success Response (200)**:
```json
{
  "success": true,
  "message": "Token is valid",
  "data": {
    "user": { /* user object */ },
    "tokenInfo": {
      "id": 1,
      "email": "<EMAIL>",
      "roleId": 2,
      "roleName": "user",
      "iat": 1640995200,
      "exp": 1640996100
    }
  }
}
```

## User Management Endpoints

### GET /api/users
**Description**: Get all users (Admin only)

**Headers**: `Authorization: Bearer <access_token>`

**Query Parameters**:
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10, max: 100)
- `role`: Filter by role (admin, manager, user)

**Success Response (200)**:
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "username": "johndoe",
      "email": "<EMAIL>",
      "firstName": "John",
      "middleName": "",
      "lastName": "Doe",
      "role": "user",
      "roleId": 2,
      "roleName": "user",
      "status": "active",
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    }
  ],
  "pagination": {
    "currentPage": 1,
    "totalPages": 5,
    "totalItems": 50,
    "itemsPerPage": 10
  }
}
```

### GET /api/users/:id
**Description**: Get user by ID (Admin only)

**Headers**: `Authorization: Bearer <access_token>`

**Success Response (200)**:
```json
{
  "success": true,
  "data": {
    "id": 1,
    "username": "johndoe",
    "email": "<EMAIL>",
    "firstName": "John",
    "middleName": "",
    "lastName": "Doe",
    "role": "user",
    "roleId": 2,
    "roleName": "user",
    "status": "active",
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  }
}
```

### POST /api/users
**Description**: Create new user (Admin only)

**Headers**: `Authorization: Bearer <access_token>`

**Request Body**:
```json
{
  "username": "newuser",
  "email": "<EMAIL>",
  "password": "password123",
  "firstName": "New",
  "middleName": "",
  "lastName": "User",
  "roleId": 2
}
```

**Success Response (201)**:
```json
{
  "success": true,
  "message": "User created successfully",
  "data": { /* user object */ }
}
```

### PATCH /api/users/:id
**Description**: Update user details (Admin only)

**Headers**: `Authorization: Bearer <access_token>`

**Request Body** (partial update):
```json
{
  "firstName": "Updated",
  "lastName": "Name",
  "roleId": 3
}
```

**Success Response (200)**:
```json
{
  "success": true,
  "message": "User updated successfully",
  "data": { /* updated user object */ }
}
```

### DELETE /api/users/:id
**Description**: Delete user (Admin only)

**Headers**: `Authorization: Bearer <access_token>`

**Success Response (200)**:
```json
{
  "success": true,
  "message": "User deleted successfully"
}
```

## Task Management Endpoints

### GET /api/tasks
**Description**: Get tasks based on user role and permissions

**Headers**: `Authorization: Bearer <access_token>`

**Query Parameters**:
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10, max: 100)
- `status`: Filter by status (pending, in_progress, completed, cancelled)
- `priority`: Filter by priority (low, medium, high, urgent)
- `assignedTo`: Filter by assigned user ID
- `createdBy`: Filter by creator user ID

**Success Response (200)**:
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "title": "Task Title",
      "description": "Task description",
      "status": "pending",
      "priority": "medium",
      "deadline": "2024-12-31T23:59:59.000Z",
      "createdBy": 1,
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z",
      "assignedUsers": [2, 3]
    }
  ],
  "pagination": {
    "currentPage": 1,
    "totalPages": 3,
    "totalItems": 25,
    "itemsPerPage": 10
  }
}
```

### GET /api/tasks/:id
**Description**: Get task by ID

**Headers**: `Authorization: Bearer <access_token>`

**Success Response (200)**:
```json
{
  "success": true,
  "data": {
    "id": 1,
    "title": "Task Title",
    "description": "Task description",
    "status": "pending",
    "priority": "medium",
    "deadline": "2024-12-31T23:59:59.000Z",
    "createdBy": 1,
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z",
    "assignedUsers": [2, 3]
  }
}
```

### POST /api/tasks
**Description**: Create new task (Manager/Admin only)

**Headers**: `Authorization: Bearer <access_token>`

**Request Body**:
```json
{
  "title": "New Task",
  "description": "Task description",
  "priority": 3,
  "deadline": "2024-12-31T23:59:59.000Z",
  "assignedUsers": [2, 3]
}
```

**Success Response (201)**:
```json
{
  "success": true,
  "message": "Task created successfully",
  "data": { /* task object */ }
}
```

### PATCH /api/tasks/:id
**Description**: Update task (Manager/Admin or task creator only)

**Headers**: `Authorization: Bearer <access_token>`

**Request Body** (partial update):
```json
{
  "title": "Updated Task Title",
  "status": "in_progress",
  "priority": 4,
  "assignedUsers": [2, 3, 4]
}
```

**Success Response (200)**:
```json
{
  "success": true,
  "message": "Task updated successfully",
  "data": { /* updated task object */ }
}
```

### DELETE /api/tasks/:id
**Description**: Delete task (Manager/Admin only)

**Headers**: `Authorization: Bearer <access_token>`

**Success Response (200)**:
```json
{
  "success": true,
  "message": "Task deleted successfully"
}
```

### PATCH /api/tasks/:id/status
**Description**: Update task status (Any authenticated user for assigned tasks)

**Headers**: `Authorization: Bearer <access_token>`

**Request Body**:
```json
{
  "status": "completed"
}
```

**Success Response (200)**:
```json
{
  "success": true,
  "message": "Task status updated successfully",
  "data": { /* updated task object */ }
}
```

## Data Models

### User Model
```typescript
interface User {
  id: number;
  username: string;
  email: string;
  firstName: string;
  middleName?: string;
  lastName: string;
  role: 'admin' | 'manager' | 'user';
  roleId: 1 | 2 | 3; // 1=Admin, 2=User, 3=Manager
  roleName: 'admin' | 'manager' | 'user';
  status: 'active' | 'suspended' | 'deleted';
  createdAt: string;
  updatedAt: string;
}
```

### Task Model
```typescript
interface Task {
  id: number;
  title: string;
  description?: string;
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  deadline?: string;
  createdBy: number;
  createdAt: string;
  updatedAt: string;
  assignedUsers: number[];
}
```

## Error Response Format
```json
{
  "error": "Error message",
  "code": "ERROR_CODE",
  "details": ["Detailed error messages"]
}
```

## Common Error Codes
- `TOKEN_MISSING`: No authorization token provided
- `TOKEN_EXPIRED`: Access token has expired
- `TOKEN_INVALID`: Invalid token format or signature
- `TOKEN_BLACKLISTED`: Token has been invalidated
- `INVALID_TOKEN_TYPE`: Wrong token type (access vs refresh)
- `USER_NOT_FOUND`: User does not exist
- `INVALID_CREDENTIALS`: Wrong email/password
- `VALIDATION_ERROR`: Request validation failed
- `INSUFFICIENT_PERMISSIONS`: User lacks required permissions
- `AUTH_REQUIRED`: Authentication required for this endpoint

## Authentication Flow
1. User logs in with email/password
2. Backend returns access token (15min) and refresh token (7 days)
3. Frontend includes access token in Authorization header
4. When access token expires, use refresh token to get new access token
5. If refresh token expires, redirect to login
6. On logout, access token is blacklisted
