# Task Management System

A full-stack task management application with role-based access control (RBAC).

## Features

- **Three User Roles**: <PERSON><PERSON>, Manager, User
- **Role-Based Permissions**: Granular access control for different operations
- **Task Management**: Create, assign, and track tasks with deadlines
- **Email Notifications**: Automatic notifications for task assignments
- **Modern Tech Stack**: Vue.js frontend + Express.js backend + PostgreSQL

## Project Structure

```
├── frontend/                 # Vue.js frontend application
│   ├── src/
│   │   ├── components/      # Reusable Vue components
│   │   ├── views/          # Page components
│   │   ├── router/         # Vue Router configuration
│   │   ├── store/          # Pinia state management
│   │   └── api/            # API service layer
│   └── package.json
├── backend/                 # Express.js backend API
│   ├── src/
│   │   ├── routes/         # API route handlers
│   │   ├── middleware/     # Custom middleware (auth, RBAC, etc.)
│   │   ├── models/         # Database models
│   │   ├── services/       # Business logic services
│   │   ├── config/         # Configuration files
│   │   ├── utils/          # Utility functions
│   │   └── tests/          # Test files
│   └── package.json
└── package.json            # Root package.json for workspace management
```

## Quick Start

1. **Install dependencies**:
   ```bash
   npm run install:all
   ```

2. **Set up environment variables**:
   ```bash
   cp backend/.env.example backend/.env
   # Edit backend/.env with your database and email credentials
   ```

3. **Start development servers**:
   ```bash
   # Start both frontend and backend
   npm run dev:all

   # Or start individually
   npm run dev:frontend  # Vue.js dev server (port 5173)
   npm run dev:backend   # Express.js server (port 3000)
   ```

## User Roles & Permissions

| Capability | Admin | Manager | User |
|------------|-------|---------|------|
| User CRUD | ✅ | ❌ | ❌ |
| Task CRUD | ✅ | ✅ | ❌ |
| Assign Tasks | ✅ | ✅ | ❌ |
| View Tasks | All | Own + Created | Assigned Only |
| Update Task Status | ✅ | ✅ | Own Assignments |
| Email Notifications | - | - | ✅ |

## Tech Stack

**Frontend:**
- Vue.js 3 with Composition API
- Vue Router for routing
- Pinia for state management
- Axios for HTTP requests
- Vite for build tooling

**Backend:**
- Node.js with Express.js
- PostgreSQL database
- JWT authentication
- Nodemailer for emails
- Joi for validation

## Next Steps

1. Set up PostgreSQL database
2. Implement authentication middleware
3. Create user management API
4. Build task management API
5. Add email notification service
6. Develop frontend components