{"name": "task-management-backend", "version": "0.1.0", "description": "Express.js backend API for task management system", "type": "module", "main": "src/server.js", "scripts": {"dev": "nodemon src/server.js", "start": "node src/server.js", "test": "jest", "test:watch": "jest --watch", "db:init": "node scripts/init-db.js", "db:seed": "node scripts/init-db.js --seed", "db:reset": "node scripts/init-db.js --reset --seed", "db:status": "node scripts/init-db.js --status"}, "dependencies": {"bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "express-rate-limit": "^7.4.1", "helmet": "^8.0.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "nodemailer": "^6.9.18", "pg": "^8.13.1"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.1.10", "supertest": "^7.0.0"}}