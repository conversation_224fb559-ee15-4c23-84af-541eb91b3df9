# JWT Authentication Implementation Guide

## Overview

This document provides a comprehensive guide for implementing JWT authentication in your task management system. The backend now includes industry-standard JWT authentication with the following features:

- **Secure Login/Logout**: Password hashing with bcrypt, JWT tokens with expiration
- **Token Refresh**: Automatic token renewal without re-authentication
- **Role-Based Access Control**: Admin, Manager, and User roles with different permissions
- **Token Blacklisting**: Secure logout with token invalidation
- **Frontend-Ready**: All responses formatted for easy frontend consumption

## API Endpoints

### Authentication Endpoints

#### 1. Login
```http
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response (200):**
```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    "user": {
      "id": 1,
      "username": "johndoe",
      "email": "<EMAIL>",
      "firstName": "<PERSON>",
      "lastName": "Doe",
      "roleId": 2,
      "roleName": "User",
      "status": "active"
    },
    "tokens": {
      "accessToken": "eyJhbGciOiJIUzI1NiIs...",
      "refreshToken": "eyJhbGciOiJIUzI1NiIs...",
      "expiresIn": "15m"
    }
  }
}
```

#### 2. Register
```http
POST /api/auth/register
Content-Type: application/json

{
  "username": "johndoe",
  "email": "<EMAIL>",
  "password": "password123",
  "firstName": "John",
  "lastName": "Doe",
  // "middleName": "Optional",
  "roleId": 2 [1=Admin, 2=User, 3=Manager]
}
```

#### 3. Refresh Token
```http
POST /api/auth/refresh
Content-Type: application/json

{
  "refreshToken": "eyJhbGciOiJIUzI1NiIs..."
}
```

**Response (200):**
```json
{
  "success": true,
  "message": "Token refreshed successfully",
  "data": {
    "accessToken": "eyJhbGciOiJIUzI1NiIs...",
    "expiresIn": "15m"
  }
}
```

#### 4. Logout
```http
POST /api/auth/logout
Authorization: Bearer <access_token>
```

#### 5. Get Current User
```http
GET /api/auth/me
Authorization: Bearer <access_token>
```

#### 6. Verify Token
```http
POST /api/auth/verify
Content-Type: application/json

{
  "token": "eyJhbGciOiJIUzI1NiIs..."
}
```

## Frontend Integration

### JavaScript/Axios Example

```javascript
class AuthService {
  constructor() {
    this.baseURL = 'http://localhost:3000/api';
    this.accessToken = localStorage.getItem('accessToken');
    this.refreshToken = localStorage.getItem('refreshToken');
  }

  // Login user
  async login(email, password) {
    try {
      const response = await axios.post(`${this.baseURL}/auth/login`, {
        email,
        password
      });

      if (response.data.success) {
        const { accessToken, refreshToken } = response.data.data.tokens;
        this.setTokens(accessToken, refreshToken);
        return response.data.data.user;
      }
    } catch (error) {
      throw new Error(error.response?.data?.error || 'Login failed');
    }
  }

  // Register user
  async register(userData) {
    try {
      const response = await axios.post(`${this.baseURL}/auth/register`, userData);
      
      if (response.data.success) {
        const { accessToken, refreshToken } = response.data.data.tokens;
        this.setTokens(accessToken, refreshToken);
        return response.data.data.user;
      }
    } catch (error) {
      throw new Error(error.response?.data?.error || 'Registration failed');
    }
  }

  // Refresh access token
  async refreshAccessToken() {
    try {
      const response = await axios.post(`${this.baseURL}/auth/refresh`, {
        refreshToken: this.refreshToken
      });

      if (response.data.success) {
        this.accessToken = response.data.data.accessToken;
        localStorage.setItem('accessToken', this.accessToken);
        return this.accessToken;
      }
    } catch (error) {
      this.logout();
      throw new Error('Session expired. Please login again.');
    }
  }

  // Logout user
  async logout() {
    try {
      if (this.accessToken) {
        await axios.post(`${this.baseURL}/auth/logout`, {}, {
          headers: { Authorization: `Bearer ${this.accessToken}` }
        });
      }
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      this.clearTokens();
    }
  }

  // Get current user
  async getCurrentUser() {
    try {
      const response = await this.makeAuthenticatedRequest('GET', '/auth/me');
      return response.data.data.user;
    } catch (error) {
      throw new Error('Failed to get user info');
    }
  }

  // Make authenticated request with automatic token refresh
  async makeAuthenticatedRequest(method, url, data = null) {
    try {
      const config = {
        method,
        url: `${this.baseURL}${url}`,
        headers: { Authorization: `Bearer ${this.accessToken}` }
      };

      if (data) config.data = data;

      return await axios(config);
    } catch (error) {
      if (error.response?.status === 401 && error.response?.data?.code === 'TOKEN_EXPIRED') {
        // Try to refresh token
        await this.refreshAccessToken();
        
        // Retry original request
        const config = {
          method,
          url: `${this.baseURL}${url}`,
          headers: { Authorization: `Bearer ${this.accessToken}` }
        };

        if (data) config.data = data;

        return await axios(config);
      }
      throw error;
    }
  }

  // Helper methods
  setTokens(accessToken, refreshToken) {
    this.accessToken = accessToken;
    this.refreshToken = refreshToken;
    localStorage.setItem('accessToken', accessToken);
    localStorage.setItem('refreshToken', refreshToken);
  }

  clearTokens() {
    this.accessToken = null;
    this.refreshToken = null;
    localStorage.removeItem('accessToken');
    localStorage.removeItem('refreshToken');
  }

  isAuthenticated() {
    return !!this.accessToken;
  }
}

// Usage example
const authService = new AuthService();

// Login
try {
  const user = await authService.login('<EMAIL>', 'password123');
  console.log('Logged in user:', user);
} catch (error) {
  console.error('Login error:', error.message);
}

// Make authenticated API calls
try {
  const response = await authService.makeAuthenticatedRequest('GET', '/users');
  console.log('Users:', response.data);
} catch (error) {
  console.error('API error:', error.message);
}
```

## Role-Based Access Control

### User Roles
- **Admin (roleId: 1)**: Full access to all endpoints
- **Manager (roleId: 3)**: Can create/manage tasks, cannot manage users
- **User (roleId: 2)**: Can view assigned tasks and update status

### Protected Routes
- `/api/users/*` - Admin only
- `/api/tasks` (POST, PATCH, DELETE) - Manager/Admin only
- `/api/tasks/:id/assign` - Manager/Admin only
- `/api/tasks/:id/status` - All authenticated users (with restrictions)

## Security Features

1. **Password Hashing**: bcrypt with 12 salt rounds
2. **JWT Security**: Signed tokens with issuer/audience validation
3. **Token Expiration**: 15-minute access tokens, 7-day refresh tokens
4. **Token Blacklisting**: Logout invalidates tokens
5. **Role Validation**: Case-insensitive role checking
6. **Input Validation**: Joi schema validation on all inputs

## Error Codes

- `TOKEN_MISSING`: No authorization header provided
- `TOKEN_EXPIRED`: Access token has expired
- `TOKEN_INVALID`: Malformed or invalid token
- `TOKEN_BLACKLISTED`: Token has been invalidated (logout)
- `INVALID_CREDENTIALS`: Wrong email/password
- `USER_NOT_FOUND`: User account doesn't exist
- `INSUFFICIENT_PERMISSIONS`: User lacks required role
- `VALIDATION_ERROR`: Request data validation failed

## Environment Variables

```env
JWT_SECRET=your_super_secret_jwt_key_here
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d
```

## Testing the API

Use the provided test credentials:
- **Admin**: `<EMAIL>` / `pass1234`
- **User**: `<EMAIL>` / `testpassword123`

All authentication endpoints are now fully functional and ready for frontend integration!
