# API Testing Guide

This guide provides comprehensive examples for testing all API endpoints using curl, Postman, or similar tools.

## Quick Start

1. **initialise Database**:
   ```bash
   npm run db:reset  # Creates tables and sample data
   ```

2. **Start Server**:
   ```bash
   npm run dev
   ```

3. **Test Health Check**:
   ```bash
   curl http://localhost:3000/health
   ```

## Authentication

For testing purposes, the API uses mock authentication via headers or query parameters:

- **Header Method**: `x-user-id: 1` and `x-user-role: admin`
- **Query Method**: `?userId=1&userRole=admin`

### Sample Users (from your existing database):
- **Admin**: ID=1, Role=Admin, Username=kimeudom
- **User**: ID=2, Role=User, Username=mary_njeri

## User Management API

### 1. Get All Users

```bash
# Get all users with pagination
curl "http://localhost:3000/api/users?page=1&limit=10"

# Filter by role
curl "http://localhost:3000/api/users?role=manager"
```

**Response**:
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "email": "<EMAIL>",
      "firstName": "Admin",
      "lastName": "User",
      "role": "admin",
      "isActive": true,
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 3,
    "pages": 1
  }
}
```

### 2. Create User

```bash
curl -X POST http://localhost:3000/api/users \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123",
    "firstName": "New",
    "lastName": "User",
    "role": "user"
  }'
```

### 3. Get User by ID

```bash
curl http://localhost:3000/api/users/1
```

### 4. Update User

```bash
curl -X PATCH http://localhost:3000/api/users/1 \
  -H "Content-Type: application/json" \
  -d '{
    "firstName": "Updated",
    "role": "manager"
  }'
```

### 5. Delete User

```bash
curl -X DELETE http://localhost:3000/api/users/1
```

## Task Management API

### 1. Get All Tasks

```bash
# As admin (sees all tasks)
curl "http://localhost:3000/api/tasks?userId=1&userRole=admin"

# As manager (sees own and assigned tasks)
curl "http://localhost:3000/api/tasks?userId=2&userRole=manager"

# As user (sees only assigned tasks)
curl "http://localhost:3000/api/tasks?userId=3&userRole=user"

# With filters
curl "http://localhost:3000/api/tasks?userId=1&userRole=admin&status=pending&priority=high"
```

**Response**:
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "title": "Setup Project Environment",
      "description": "initialise the development environment",
      "status": "completed",
      "priority": "high",
      "dueDate": "2024-01-08T00:00:00.000Z",
      "createdBy": 1,
      "assignedTo": 2,
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z",
      "creator": {
        "firstName": "Admin",
        "lastName": "User"
      },
      "assignee": {
        "firstName": "Manager",
        "lastName": "User"
      }
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 4,
    "pages": 1
  }
}
```

### 2. Create Task

```bash
# As manager or admin
curl -X POST http://localhost:3000/api/tasks \
  -H "Content-Type: application/json" \
  -H "x-user-id: 2" \
  -H "x-user-role: manager" \
  -d '{
    "title": "New Task",
    "description": "Task description here",
    "priority": "medium",
    "dueDate": "2024-12-31T23:59:59.000Z",
    "assignedTo": 3
  }'
```

### 3. Get Task by ID

```bash
curl -H "x-user-id: 1" -H "x-user-role: admin" \
  http://localhost:3000/api/tasks/1
```

### 4. Update Task

```bash
curl -X PATCH http://localhost:3000/api/tasks/1 \
  -H "Content-Type: application/json" \
  -H "x-user-id: 2" \
  -H "x-user-role: manager" \
  -d '{
    "title": "Updated Task Title",
    "status": "in_progress",
    "priority": "high"
  }'
```

### 5. Update Task Status

```bash
# User updating status of assigned task
curl -X PATCH http://localhost:3000/api/tasks/1/status \
  -H "Content-Type: application/json" \
  -H "x-user-id: 3" \
  -H "x-user-role: user" \
  -d '{
    "status": "completed"
  }'
```

### 6. Assign Task

```bash
# Manager or admin assigning task
curl -X POST http://localhost:3000/api/tasks/1/assign \
  -H "Content-Type: application/json" \
  -H "x-user-id: 2" \
  -H "x-user-role: manager" \
  -d '{
    "userId": 3
  }'
```

### 7. Unassign Task

```bash
curl -X DELETE http://localhost:3000/api/tasks/1/assign \
  -H "x-user-id: 2" \
  -H "x-user-role: manager"
```

### 8. Delete Task

```bash
# Admin can delete any task, manager can delete own tasks
curl -X DELETE http://localhost:3000/api/tasks/1 \
  -H "x-user-id: 1" \
  -H "x-user-role: admin"
```

## Error Responses

### Validation Error
```json
{
  "error": "Validation failed",
  "details": ["\"email\" must be a valid email"]
}
```

### Permission Error
```json
{
  "error": "Insufficient permissions. Only managers and admins can create tasks."
}
```

### Not Found Error
```json
{
  "error": "Task not found"
}
```

## Role-Based Access Control

### Admin Role
- ✅ Full access to all users and tasks
- ✅ Create, read, update, delete any resource
- ✅ Assign/unassign tasks

### Manager Role
- ✅ Create and manage tasks
- ✅ View tasks they created or are assigned to
- ✅ Assign/unassign tasks they created
- ✅ View all users
- ❌ Cannot manage other managers' tasks (unless assigned)

### User Role
- ✅ View tasks assigned to them
- ✅ Update status of assigned tasks
- ❌ Cannot create tasks
- ❌ Cannot assign/unassign tasks
- ❌ Cannot view other users' tasks

## Testing with Postman

1. **Import Collection**: Create a new Postman collection
2. **Set Environment Variables**:
   - `baseUrl`: `http://localhost:3000`
   - `adminUserId`: `1`
   - `managerUserId`: `2`
   - `regularUserId`: `3`

3. **Add Headers** to requests:
   - `Content-Type`: `application/json`
   - `x-user-id`: `{{adminUserId}}`
   - `x-user-role`: `admin`

## Database Status Check

```bash
# Check database connection and schema status
curl http://localhost:3000/health
```

**Response**:
```json
{
  "status": "OK",
  "timestamp": "2024-01-01T12:00:00.000Z",
  "database": {
    "connected": true,
    "initialised": true,
    "tables": ["users", "tasks"]
  }
}
```

## Common Testing Scenarios

### 1. Complete User Workflow
```bash
# 1. Create a new user
curl -X POST http://localhost:3000/api/users \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password123", "firstName": "Test", "lastName": "User"}'

# 2. Get the user (note the ID from response)
curl http://localhost:3000/api/users/4

# 3. Update the user
curl -X PATCH http://localhost:3000/api/users/4 \
  -H "Content-Type: application/json" \
  -d '{"role": "manager"}'
```

### 2. Complete Task Workflow
```bash
# 1. Create task as manager
curl -X POST http://localhost:3000/api/tasks \
  -H "Content-Type: application/json" \
  -H "x-user-id: 2" -H "x-user-role: manager" \
  -d '{"title": "Test Task", "description": "Testing workflow", "assignedTo": 3}'

# 2. User views assigned task
curl -H "x-user-id: 3" -H "x-user-role: user" \
  http://localhost:3000/api/tasks

# 3. User updates task status
curl -X PATCH http://localhost:3000/api/tasks/5/status \
  -H "Content-Type: application/json" \
  -H "x-user-id: 3" -H "x-user-role: user" \
  -d '{"status": "in_progress"}'

# 4. Manager checks task progress
curl -H "x-user-id: 2" -H "x-user-role: manager" \
  http://localhost:3000/api/tasks/5
```

### 3. Permission Testing
```bash
# Try to create task as regular user (should fail)
curl -X POST http://localhost:3000/api/tasks \
  -H "Content-Type: application/json" \
  -H "x-user-id: 3" -H "x-user-role: user" \
  -d '{"title": "Unauthorized Task"}'

# Try to view other user's task (should fail)
curl -H "x-user-id: 3" -H "x-user-role: user" \
  http://localhost:3000/api/tasks/1
```

## Troubleshooting

### Database Connection Issues
```bash
# Check database status
npm run db:status

# Reset database if needed
npm run db:reset
```

### Common Error Codes
- **400**: Bad Request (validation errors)
- **401**: Unauthorized (missing authentication)
- **403**: Forbidden (insufficient permissions)
- **404**: Not Found (resource doesn't exist)
- **409**: Conflict (duplicate email, etc.)
- **500**: Internal Server Error
