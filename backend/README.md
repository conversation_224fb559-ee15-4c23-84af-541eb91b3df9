# Task Management System - Backend API

A robust Express.js backend API for a task management system with role-based access control, PostgreSQL database integration, and comprehensive CRUD operations.

## Features

- 🔐 **Role-Based Access Control**: Admin, Manager, and User roles with different permissions
- 📊 **PostgreSQL Database**: Robust database with proper schema and relationships
- 🛡️ **Security**: Helmet, CORS, rate limiting, and input validation
- 📝 **Comprehensive API**: Full CRUD operations for users and tasks
- 🧪 **Testing Ready**: Mock authentication for easy API testing
- 📋 **Database Management**: Automated schema initialization and seeding
- 🚀 **Production Ready**: Error handling, logging, and graceful shutdown

## Quick Start

### Prerequisites

- Node.js (v18 or higher)
- PostgreSQL (v12 or higher)
- npm or yarn

### Installation

1. **Clone and Install Dependencies**:
   ```bash
   cd backend
   npm install
   ```

2. **Configure Environment**:
   ```bash
   cp .env.example .env
   # Edit .env with your database credentials
   ```

3. **Setup Database**:
   ```bash
   # Create database in PostgreSQL
   createdb task_management_db

   # initialise schema and seed data
   npm run db:reset
   ```

4. **Start Development Server**:
   ```bash
   npm run dev
   ```

5. **Verify Setup**:
   ```bash
   curl http://localhost:3000/health
   ```

## Environment Configuration

Create a `.env` file with the following variables:

```env
# Server Configuration
PORT=3000
NODE_ENV=development
FRONTEND_URL=http://localhost:5173

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=task_management_db
DB_USER=your_username
DB_PASSWORD=your_password

# JWT Configuration (for future authentication)
JWT_SECRET=your_super_secret_jwt_key_here
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d

# Email Configuration (for future notifications)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your_app_password
EMAIL_FROM=<EMAIL>
```

## Database Management

### Available Scripts

```bash
npm run db:status    # Check database connection and schema status
npm run db:init      # initialise database schema
npm run db:seed      # initialise schema and add sample data
npm run db:reset     # Drop all tables, reinitialise, and seed
```

### Database Schema

#### Users Table
- `id` (Primary Key)
- `email` (Unique)
- `password` (Hashed)
- `first_name`, `last_name`
- `role` (admin, manager, user)
- `is_active` (Boolean)
- `created_at`, `updated_at`

#### Tasks Table
- `id` (Primary Key)
- `title`, `description`
- `status` (pending, in_progress, completed, cancelled)
- `priority` (low, medium, high, urgent)
- `due_date`
- `created_by` (Foreign Key to users)
- `assigned_to` (Foreign Key to users)
- `created_at`, `updated_at`

## API Endpoints

### Health Check
- `GET /health` - Server and database status

### User Management
- `GET /api/users` - List all users (with pagination)
- `POST /api/users` - Create new user
- `GET /api/users/:id` - Get user by ID
- `PATCH /api/users/:id` - Update user
- `DELETE /api/users/:id` - Delete user (soft delete)

### Task Management
- `GET /api/tasks` - List tasks (role-based filtering)
- `POST /api/tasks` - Create new task (managers/admins only)
- `GET /api/tasks/:id` - Get task by ID
- `PATCH /api/tasks/:id` - Update task
- `DELETE /api/tasks/:id` - Delete task
- `PATCH /api/tasks/:id/status` - Update task status
- `POST /api/tasks/:id/assign` - Assign task to user
- `DELETE /api/tasks/:id/assign` - Unassign task

## Role-Based Permissions

### Admin
- Full access to all users and tasks
- Can create, read, update, delete any resource
- Can assign/unassign any task

### Manager
- Can create and manage tasks
- Can view tasks they created or are assigned to
- Can assign/unassign tasks they created
- Can view all users

### User
- Can view tasks assigned to them
- Can update status of assigned tasks
- Cannot create, assign, or delete tasks

## Testing the API

For detailed testing instructions and examples, see [API_TESTING.md](./API_TESTING.md).

### Quick Test

```bash
# Check server status
curl http://localhost:3000/health

# Get all users
curl http://localhost:3000/api/users

# Get tasks as admin
curl -H "x-user-id: 1" -H "x-user-role: admin" \
  http://localhost:3000/api/tasks
```

### Sample Data (after seeding)

**Users**:
- `<EMAIL>` (ID: 1, Role: admin)
- `<EMAIL>` (ID: 2, Role: manager)
- `<EMAIL>` (ID: 3, Role: user)

**Password for all sample users**: `password123`

## Development

### Available Scripts

```bash
npm run dev          # Start development server with nodemon
npm start            # Start production server
npm test             # Run tests
npm run test:watch   # Run tests in watch mode
```

### Project Structure

```
backend/
├── src/
│   ├── config/          # Database and app configuration
│   ├── middleware/      # Express middleware
│   ├── models/          # Database models and schema
│   ├── routes/          # API route handlers
│   ├── services/        # Business logic services
│   ├── utils/           # Utility functions
│   └── server.js        # Main application entry point
├── scripts/             # Database and utility scripts
├── tests/               # Test files
└── package.json
```

## Security Features

- **Helmet**: Security headers
- **CORS**: Cross-origin resource sharing
- **Rate Limiting**: Prevents abuse
- **Input Validation**: Joi schema validation
- **Password Hashing**: bcrypt with salt rounds
- **SQL Injection Prevention**: Parameterized queries

## Error Handling

The API includes comprehensive error handling:

- Validation errors (400)
- Authentication errors (401)
- Permission errors (403)
- Not found errors (404)
- Conflict errors (409)
- Database errors (500)

## Production Deployment

1. Set `NODE_ENV=production`
2. Use a production PostgreSQL database
3. Set strong JWT secrets
4. Configure proper CORS origins
5. Set up SSL/TLS
6. Use a process manager (PM2)
7. Set up monitoring and logging

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

MIT License - see LICENSE file for details
