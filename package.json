{"name": "task-management-system", "version": "0.1.0", "description": "Full-stack task management system", "main": "index.js", "engines": {"node": ">=18.0.0"}, "packageManager": "npm@10", "type": "module", "scripts": {"dev": "npm run dev:frontend", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && npm run dev", "dev:all": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "install:all": "npm install && cd frontend && npm install && cd ../backend && npm install", "test": "npm run test:backend && npm run test:frontend", "test:backend": "cd backend && npm test", "test:frontend": "cd frontend && npm test"}, "repository": {"type": "git", "url": "git+https://github.com/kimeudom/task_management_Cytonn.git"}, "keywords": [], "author": "<PERSON>", "license": "ISC", "bugs": {"url": "https://github.com/kimeudom/task_management_Cytonn/issues"}, "homepage": "https://github.com/kimeudom/task_management_Cytonn#readme", "devDependencies": {"concurrently": "^8.2.2"}}